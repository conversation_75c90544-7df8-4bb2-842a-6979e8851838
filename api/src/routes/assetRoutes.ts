import express from 'express';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// Serve <PERSON>WRM logo assets
router.get('/qwrm-logo-:color.svg', (req, res) => {
  const { color } = req.params;
  const validColors = ['yellow', 'teal', 'green', 'blue', 'red'];

  if (!validColors.includes(color)) {
    return res.status(404).json({ error: 'Logo color not found' });
  }

  const logoPath = path.join(__dirname, `../assets/icons/qwrm-logo-${color}.svg`);

  if (!fs.existsSync(logoPath)) {
    return res.status(404).json({ error: 'Logo file not found' });
  }

  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
  res.sendFile(logoPath);
});

// Serve <PERSON> logo as PNG (converted from SVG for email compatibility)
router.get('/qwrm-logo-:color.png', (req, res) => {
  const { color } = req.params;
  const validColors = ['yellow', 'teal', 'green', 'blue', 'red'];

  if (!validColors.includes(color)) {
    return res.status(404).json({ error: 'Logo color not found' });
  }

  // For now, serve the SVG with PNG content-type as a fallback
  // In production, you might want to convert SVG to PNG using a library like sharp
  const logoPath = path.join(__dirname, `../assets/icons/qwrm-logo-${color}.svg`);

  if (!fs.existsSync(logoPath)) {
    return res.status(404).json({ error: 'Logo file not found' });
  }

  // Read the SVG content
  const svgContent = fs.readFileSync(logoPath, 'utf8');

  // For email compatibility, we'll serve SVG content but with proper headers
  // Email clients that support SVG will render it, others will show alt text
  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
  res.send(svgContent);
});

// Generic asset serving for other static files
router.get('/:filename', (req, res) => {
  const { filename } = req.params;

  // Security: prevent directory traversal
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return res.status(400).json({ error: 'Invalid filename' });
  }

  // Look for the file in the assets directory
  const assetPath = path.join(__dirname, '../assets', filename);

  if (!fs.existsSync(assetPath)) {
    return res.status(404).json({ error: 'Asset not found' });
  }

  // Set appropriate content type based on file extension
  const ext = path.extname(filename).toLowerCase();
  const contentTypes: { [key: string]: string } = {
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.webp': 'image/webp'
  };

  const contentType = contentTypes[ext] || 'application/octet-stream';
  res.setHeader('Content-Type', contentType);
  res.setHeader('Cache-Control', 'public, max-age=31536000'); // Cache for 1 year
  res.sendFile(assetPath);
});

export default router;
