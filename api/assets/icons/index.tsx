import React from 'react';
import Svg, { <PERSON>, <PERSON>, De<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Rect } from 'react-native-svg';

interface IconProps {
  color?: string;
  size?: number;
}

// Calendar Icon
export const CalendarIcon = ({ color = "#00788D", size = 18 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 17/18)} viewBox="0 0 18 17" fill="none">
      <Path
        d="M15.611 1.889H14.667V3.778H11.833V1.889H6.167V3.778H3.333V1.889H2.389C1.88841 1.89032 1.4087 2.08976 1.05473 2.44373C0.700763 2.7977 0.50132 3.27741 0.5 3.778V15.111C0.50132 15.6116 0.700763 16.0913 1.05473 16.4453C1.4087 16.7992 1.88841 16.9987 2.389 17H15.611C16.1116 16.9987 16.5913 16.7992 16.9453 16.4453C17.2992 16.0913 17.4987 15.6116 17.5 15.111V3.778C17.4987 3.27741 17.2992 2.7977 16.9453 2.44373C16.5913 2.08976 16.1116 1.89032 15.611 1.889ZM15.611 15.111H2.389V7.556H15.611V15.111ZM5.694 0H3.806V3.306H5.694V0ZM14.194 0H12.306V3.306H14.195V0H14.194Z"
        fill={color}
      />
    </Svg>
  );
};

// Comment Icon
export const CommentIcon = ({ color = "#328DDB", size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 30 30" fill="none">
      <G clipPath="url(#clip0_50_2743)">
        <Path
          d="M7.24222 22.9277C7.99808 22.377 8.97659 22.2363 9.8555 22.5527C11.4082 23.1152 13.1485 23.4375 15 23.4375C22.3067 23.4375 27.1875 18.7207 27.1875 14.0625C27.1875 9.4043 22.3067 4.6875 15 4.6875C7.69339 4.6875 2.81253 9.4043 2.81253 14.0625C2.81253 15.9375 3.53909 17.7422 4.90433 19.2891C5.40823 19.8574 5.65433 20.6074 5.59573 21.3691C5.5137 22.4297 5.26175 23.4023 4.93362 24.2637C5.92972 23.8008 6.75589 23.2852 7.24222 22.9336V22.9277ZM1.24222 25.3066C1.34768 25.1484 1.44729 24.9902 1.54104 24.832C2.12698 23.8594 2.68362 22.582 2.79495 21.1465C1.03714 19.1484 2.86186e-05 16.7051 2.86186e-05 14.0625C2.86186e-05 7.33008 6.71487 1.875 15 1.875C23.2852 1.875 30 7.33008 30 14.0625C30 20.7949 23.2852 26.25 15 26.25C12.8262 26.25 10.7637 25.875 8.90042 25.2012C8.20315 25.7109 7.06643 26.4082 5.71878 26.9941C4.83401 27.3809 3.8262 27.7324 2.78323 27.9375C2.73636 27.9492 2.68948 27.9551 2.64261 27.9668C2.38479 28.0137 2.13284 28.0547 1.86917 28.0781C1.85745 28.0781 1.83987 28.084 1.82815 28.084C1.52933 28.1133 1.2305 28.1309 0.931669 28.1309C0.55081 28.1309 0.210966 27.9023 0.0644818 27.5508C-0.0820026 27.1992 2.86065e-05 26.8008 0.2637 26.5312C0.503935 26.2852 0.720732 26.0215 0.92581 25.7402C1.02542 25.6055 1.11917 25.4707 1.20706 25.3359L1.22464 25.3066H1.24222Z"
          fill={color}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_50_2743">
          <Rect width="30" height="30" fill="white"/>
        </ClipPath>
      </Defs>
    </Svg>
  );
};

// Create Event Icon
export const CreateEventIcon = ({ color = "#014954", size = 21 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 23/21)} viewBox="0 0 21 23" fill="none">
      <Path
        d="M5.25 0.290527C5.6625 0.290527 6 0.609739 6 0.999887V3.12797H15V0.999887C15 0.609739 15.3375 0.290527 15.75 0.290527C16.1625 0.290527 16.5 0.609739 16.5 0.999887V3.12797H18C19.6547 3.12797 21 4.40038 21 5.9654V7.38412V8.80284V20.1526C21 21.7176 19.6547 22.99 18 22.99H3C1.34531 22.99 0 21.7176 0 20.1526V8.80284V7.38412V5.9654C0 4.40038 1.34531 3.12797 3 3.12797H4.5V0.999887C4.5 0.609739 4.8375 0.290527 5.25 0.290527ZM19.5 8.80284H1.5V20.1526C1.5 20.9373 2.17031 21.5713 3 21.5713H18C18.8297 21.5713 19.5 20.9373 19.5 20.1526V8.80284ZM18 4.54668H3C2.17031 4.54668 1.5 5.18068 1.5 5.9654V7.38412H19.5V5.9654C19.5 5.18068 18.8297 4.54668 18 4.54668ZM15 15.1871C15 15.5772 14.6625 15.8964 14.25 15.8964H11.25V18.7339C11.25 19.124 10.9125 19.4432 10.5 19.4432C10.0875 19.4432 9.75 19.124 9.75 18.7339V15.8964H6.75C6.3375 15.8964 6 15.5772 6 15.1871C6 14.7969 6.3375 14.4777 6.75 14.4777H9.75V11.6403C9.75 11.2501 10.0875 10.9309 10.5 10.9309C10.9125 10.9309 11.25 11.2501 11.25 11.6403V14.4777H14.25C14.6625 14.4777 15 14.7969 15 15.1871Z"
        fill={color}
      />
    </Svg>
  );
};

// Edit Pen Icon
export const EditPenIcon = ({ color = "rgba(1, 189, 203, 0.76)", size = 17 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 16/17)} viewBox="0 0 17 16" fill="none">
      <Path
        d="M12.8825 1.22097C13.1765 0.926874 13.6521 0.926874 13.9431 1.22097L15.2759 2.55379C15.57 2.84789 15.57 3.32345 15.2759 3.61442L13.5645 5.33207L11.1679 2.93549L12.8825 1.22097ZM10.4608 3.64258L12.8574 6.03915L6.00559 12.891V12.4968C6.00559 12.2214 5.78033 11.9962 5.505 11.9962H4.50382V10.995C4.50382 10.7197 4.27856 10.4944 4.00323 10.4944H3.60902L10.4608 3.64258ZM2.59219 11.8554C2.62974 11.7302 2.67667 11.6082 2.73924 11.4956H3.50264V12.4968C3.50264 12.7721 3.72791 12.9974 4.00323 12.9974H5.00441V13.7608C4.88865 13.8202 4.76976 13.8703 4.64148 13.9078L1.73806 14.7619L2.59219 11.8585V11.8554ZM14.6533 0.513887C13.9681 -0.171296 12.8606 -0.171296 12.1754 0.513887L2.38883 10.2973C2.03216 10.654 1.77247 11.092 1.63168 11.5738L0.520999 15.3595C0.467811 15.5347 0.51787 15.7256 0.646147 15.8539C0.774423 15.9821 0.965273 16.0322 1.14048 15.979L4.92619 14.8652C5.40801 14.7244 5.84603 14.4616 6.2027 14.108L15.9861 4.32463C16.6713 3.63945 16.6713 2.53189 15.9861 1.84671L14.6533 0.513887ZM10.8644 6.32386C11.0584 6.12989 11.0584 5.81076 10.8644 5.61678C10.6705 5.4228 10.3513 5.4228 10.1574 5.61678L6.15264 9.6215C5.95866 9.81548 5.95866 10.1346 6.15264 10.3286C6.34662 10.5226 6.66575 10.5226 6.85972 10.3286L10.8644 6.32386Z"
        fill={color}
      />
    </Svg>
  );
};

// Event Cancel Icon
export const EventCancelIcon = ({ color = "#E35D5D", size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 35/30)} viewBox="0 0 30 35" fill="none">
      <G clipPath="url(#clip0_50_2718)">
        <Path
          d="M8.57143 0C9.46205 0 10.1786 0.731445 10.1786 1.64062V4.375H19.8214V1.64062C19.8214 0.731445 20.5379 0 21.4286 0C22.3192 0 23.0357 0.731445 23.0357 1.64062V4.375H25.7143C28.0781 4.375 30 6.33691 30 8.75V9.84375V13.125V30.625C30 33.0381 28.0781 35 25.7143 35H4.28571C1.92188 35 0 33.0381 0 30.625V13.125V9.84375V8.75C0 6.33691 1.92188 4.375 4.28571 4.375H6.96429V1.64062C6.96429 0.731445 7.6808 0 8.57143 0ZM26.7857 13.125H3.21429V30.625C3.21429 31.2266 3.69643 31.7188 4.28571 31.7188H25.7143C26.3036 31.7188 26.7857 31.2266 26.7857 30.625V13.125ZM20.4241 19.209L17.2768 22.4219L20.4241 25.6348C21.0536 26.2773 21.0536 27.3164 20.4241 27.9521C19.7946 28.5879 18.7768 28.5947 18.154 27.9521L15.0067 24.7393L11.8594 27.9521C11.2299 28.5947 10.2121 28.5947 9.58928 27.9521C8.96652 27.3096 8.95982 26.2705 9.58928 25.6348L12.7366 22.4219L9.58928 19.209C8.95982 18.5664 8.95982 17.5273 9.58928 16.8916C10.2187 16.2559 11.2366 16.249 11.8594 16.8916L15.0067 20.1045L18.154 16.8916C18.7835 16.249 19.8013 16.249 20.4241 16.8916C21.0469 17.5342 21.0536 18.5732 20.4241 19.209Z"
          fill={color}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_50_2718">
          <Rect width="30" height="35" fill="white"/>
        </ClipPath>
      </Defs>
    </Svg>
  );
};

// Home Icon
export const HomeIcon = ({ color = "#014954", size = 27 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 23/27)} viewBox="0 0 27 23" fill="none">
      <Path
        d="M13.9967 0.468132C13.7155 0.233157 13.2889 0.233157 13.003 0.468132L0.252988 11.1085C-0.0563866 11.3657 -0.0891991 11.8179 0.187363 12.1105C0.463926 12.4031 0.937363 12.4341 1.24674 12.1726L2.99986 10.7095V19.4435C2.99986 21.4031 4.67799 22.9903 6.74986 22.9903H20.2499C22.3217 22.9903 23.9999 21.4031 23.9999 19.4435V10.7095L25.753 12.1726C26.0624 12.4297 26.5358 12.4031 26.8124 12.1105C27.0889 11.8179 27.0561 11.3701 26.7467 11.1085L13.9967 0.468132ZM4.49986 19.4435V9.45483L13.4999 1.94449L22.4999 9.45483V19.4435C22.4999 20.6184 21.4921 21.5716 20.2499 21.5716H17.2499V14.478C17.2499 13.6933 16.5796 13.0593 15.7499 13.0593H11.2499C10.4202 13.0593 9.74986 13.6933 9.74986 14.478V21.5716H6.74986C5.50768 21.5716 4.49986 20.6184 4.49986 19.4435ZM11.2499 21.5716V14.478H15.7499V21.5716H11.2499Z"
        fill={color}
      />
    </Svg>
  );
};

// Location Pin Icon
export const LocationPinIcon = ({ color = "#00788D", size = 14 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 19/14)} viewBox="0 0 14 19" fill="none">
      <Path
        d="M7 6.2949e-05C5.35243 -0.0732592 3.74301 0.509685 2.52456 1.6211C1.3061 2.73251 0.57805 4.28171 0.5 5.92906C0.5 11.6011 7 19.0001 7 19.0001C7 19.0001 13.5 11.6011 13.5 5.92906C13.4217 4.2818 12.6936 2.73276 11.4752 1.6214C10.2568 0.510044 8.6475 -0.072992 7 6.2949e-05ZM7 9.21006C6.1096 9.24834 5.2403 8.93225 4.58244 8.33099C3.92458 7.72974 3.53177 6.89231 3.49 6.00206C3.53177 5.11181 3.92458 4.27439 4.58244 3.67313C5.2403 3.07188 6.1096 2.75578 7 2.79406C7.89007 2.75633 8.75885 3.07266 9.41627 3.67386C10.0737 4.27506 10.4662 5.11218 10.508 6.00206C10.4662 6.89195 10.0737 7.72906 9.41627 8.33026C8.75885 8.93146 7.89007 9.2478 7 9.21006Z"
        fill={color}
      />
    </Svg>
  );
};
// Notifications Icon
export const NotificationsIcon = ({ color = "#014954", size = 21 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 25/21)} viewBox="0 0 21 25" fill="none">
      <Path
        d="M9.75 1.04053C9.75 0.628027 10.0875 0.290527 10.5 0.290527C10.9125 0.290527 11.25 0.628027 11.25 1.04053V1.82803C15.0422 2.20303 18 5.3999 18 9.29053V10.6546C18 12.703 18.8156 14.6671 20.2641 16.1202L20.3953 16.2515C20.7844 16.6405 21.0047 17.1702 21.0047 17.7187C21.0047 18.8671 20.0766 19.7952 18.9281 19.7952L2.07656 19.7905C0.928125 19.7905 0 18.8624 0 17.714C0 17.1655 0.220312 16.6358 0.609375 16.2468L0.740625 16.1155C2.18438 14.6671 3 12.703 3 10.6546V9.29053C3 5.3999 5.95781 2.20303 9.75 1.82803V1.04053ZM10.5 3.29053C7.18594 3.29053 4.5 5.97647 4.5 9.29053V10.6546C4.5 13.1015 3.52969 15.4499 1.79531 17.1796L1.66875 17.3062C1.56094 17.414 1.5 17.5593 1.5 17.714C1.5 18.0327 1.75781 18.2905 2.07656 18.2905H18.9234C19.2422 18.2905 19.5 18.0327 19.5 17.714C19.5 17.5593 19.4391 17.414 19.3312 17.3062L19.2 17.1749C17.4703 15.4452 16.4953 13.0968 16.4953 10.6499V9.28584C16.4953 5.97178 13.8094 3.28584 10.4953 3.28584L10.5 3.29053ZM9.08438 21.7921C9.29063 22.3733 9.84844 22.7905 10.5 22.7905C11.1516 22.7905 11.7094 22.3733 11.9156 21.7921C12.0516 21.403 12.4828 21.1968 12.8719 21.3327C13.2609 21.4687 13.4672 21.8999 13.3312 22.289C12.9187 23.4562 11.8078 24.2905 10.5 24.2905C9.19219 24.2905 8.08125 23.4562 7.66875 22.289C7.53281 21.8999 7.73437 21.4687 8.12812 21.3327C8.52187 21.1968 8.94844 21.3983 9.08438 21.7921Z"
        fill={color}
      />
    </Svg>
  );
};

// Payment Icon
export const PaymentIcon = ({ color = "#32DBBD", size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={size} viewBox="0 0 30 30" fill="none">
      <G clipPath="url(#clip0_50_2725)">
        <Path
          d="M27.1875 15C27.1875 11.7677 25.9035 8.66774 23.6179 6.38214C21.3323 4.09654 18.2323 2.8125 15 2.8125C11.7677 2.8125 8.66774 4.09654 6.38214 6.38214C4.09654 8.66774 2.8125 11.7677 2.8125 15C2.8125 18.2323 4.09654 21.3323 6.38214 23.6179C8.66774 25.9035 11.7677 27.1875 15 27.1875C18.2323 27.1875 21.3323 25.9035 23.6179 23.6179C25.9035 21.3323 27.1875 18.2323 27.1875 15ZM0 15C0 11.0218 1.58035 7.20644 4.3934 4.3934C7.20644 1.58035 11.0218 0 15 0C18.9782 0 22.7936 1.58035 25.6066 4.3934C28.4196 7.20644 30 11.0218 30 15C30 18.9782 28.4196 22.7936 25.6066 25.6066C22.7936 28.4196 18.9782 30 15 30C11.0218 30 7.20644 28.4196 4.3934 25.6066C1.58035 22.7936 0 18.9782 0 15ZM16.2188 7.82813V8.66016C16.7871 8.73047 17.3555 8.88867 17.918 9.04688C18.0293 9.07617 18.1348 9.10547 18.2461 9.14062C18.9199 9.32812 19.3184 10.0254 19.1309 10.6992C18.9434 11.373 18.2461 11.7656 17.5723 11.584C17.4785 11.5605 17.3906 11.5312 17.2969 11.5078C16.8867 11.3906 16.4766 11.2793 16.0605 11.1973C15.2871 11.0508 14.3906 11.1211 13.6699 11.4316C13.0254 11.7129 12.4922 12.3926 13.2246 12.8613C13.7988 13.2305 14.502 13.418 15.1699 13.5996C15.3105 13.6348 15.4453 13.6758 15.5801 13.7109C16.4941 13.9688 17.6602 14.3027 18.5332 14.9004C19.6699 15.6797 20.2031 16.9453 19.9512 18.3047C19.7109 19.6172 18.7969 20.4785 17.7012 20.9238C17.2441 21.1113 16.7461 21.2285 16.2246 21.2871V22.1777C16.2246 22.875 15.6562 23.4434 14.959 23.4434C14.2617 23.4434 13.6934 22.875 13.6934 22.1777V21.1582C12.8438 20.9648 12.0117 20.6953 11.1855 20.4258C10.5234 20.209 10.1602 19.4883 10.3828 18.8262C10.6055 18.1641 11.3203 17.8008 11.9824 18.0234C12.1289 18.0703 12.2754 18.123 12.4219 18.1699C13.084 18.3926 13.7637 18.6211 14.4434 18.7324C15.4395 18.8789 16.2363 18.791 16.7578 18.5801C17.4609 18.2988 17.7949 17.4609 17.1035 16.9922C16.5117 16.5879 15.7793 16.3887 15.082 16.2012C14.9473 16.166 14.8184 16.1309 14.6836 16.0898C13.7988 15.8379 12.6914 15.5273 11.8594 14.9941C10.7168 14.2617 10.1367 13.043 10.3828 11.6895C10.6172 10.4121 11.6133 9.5625 12.668 9.10547C12.9902 8.96484 13.3359 8.85352 13.6934 8.77148V7.82813C13.6934 7.13086 14.2617 6.5625 14.959 6.5625C15.6562 6.5625 16.2246 7.13086 16.2246 7.82813H16.2188Z"
          fill={color}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_50_2725">
          <Rect width="30" height="30" fill="white"/>
        </ClipPath>
      </Defs>
    </Svg>
  );
};

// People Icon
export const PeopleIcon = ({ color = "#014954", size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 21/30)} viewBox="0 0 30 21" fill="none">
      <Path
        d="M4.125 5.3201C4.125 4.9009 4.2123 4.48581 4.38191 4.09853C4.55152 3.71124 4.80012 3.35934 5.11351 3.06293C5.42691 2.76651 5.79897 2.53138 6.20844 2.37096C6.61792 2.21054 7.05679 2.12798 7.5 2.12798C7.94321 2.12798 8.38208 2.21054 8.79156 2.37096C9.20103 2.53138 9.57309 2.76651 9.88649 3.06293C10.1999 3.35934 10.4485 3.71124 10.6181 4.09853C10.7877 4.48581 10.875 4.9009 10.875 5.3201C10.875 5.73929 10.7877 6.15438 10.6181 6.54167C10.4485 6.92895 10.1999 7.28085 9.88649 7.57726C9.57309 7.87368 9.20103 8.10881 8.79156 8.26923C8.38208 8.42965 7.94321 8.51221 7.5 8.51221C7.05679 8.51221 6.61792 8.42965 6.20844 8.26923C5.79897 8.10881 5.42691 7.87368 5.11351 7.57726C4.80012 7.28085 4.55152 6.92895 4.38191 6.54167C4.2123 6.15438 4.125 5.73929 4.125 5.3201ZM12.375 5.3201C12.375 4.09723 11.8614 2.92444 10.9471 2.05974C10.0329 1.19504 8.79293 0.709259 7.5 0.709259C6.20707 0.709259 4.96709 1.19504 4.05285 2.05974C3.13861 2.92444 2.625 4.09723 2.625 5.3201C2.625 6.54297 3.13861 7.71575 4.05285 8.58045C4.96709 9.44515 6.20707 9.93093 7.5 9.93093C8.79293 9.93093 10.0329 9.44515 10.9471 8.58045C11.8614 7.71575 12.375 6.54297 12.375 5.3201ZM19.125 5.3201C19.125 4.47349 19.4806 3.66157 20.1135 3.06293C20.7465 2.46429 21.6049 2.12798 22.5 2.12798C23.3951 2.12798 24.2535 2.46429 24.8865 3.06293C25.5194 3.66157 25.875 4.47349 25.875 5.3201C25.875 6.1667 25.5194 6.97863 24.8865 7.57726C24.2535 8.1759 23.3951 8.51221 22.5 8.51221C21.6049 8.51221 20.7465 8.1759 20.1135 7.57726C19.4806 6.97863 19.125 6.1667 19.125 5.3201ZM27.375 5.3201C27.375 4.09723 26.8614 2.92444 25.9471 2.05974C25.0329 1.19504 23.7929 0.709259 22.5 0.709259C21.2071 0.709259 19.9671 1.19504 19.0529 2.05974C18.1386 2.92444 17.625 4.09723 17.625 5.3201C17.625 6.54297 18.1386 7.71575 19.0529 8.58045C19.9671 9.44515 21.2071 9.93093 22.5 9.93093C23.7929 9.93093 25.0329 9.44515 25.9471 8.58045C26.8614 7.71575 27.375 6.54297 27.375 5.3201ZM9.75 13.4777C12.2344 13.4777 14.25 15.3841 14.25 17.7339V18.4432C14.25 18.8334 13.9125 19.1526 13.5 19.1526H2.25C1.8375 19.1526 1.5 18.8334 1.5 18.4432V17.7339C1.5 15.3841 3.51562 13.4777 6 13.4777H9.75ZM6 12.059C2.68594 12.059 0 14.5994 0 17.7339V18.4432C0 19.6181 1.00781 20.5713 2.25 20.5713H13.5C14.7422 20.5713 15.75 19.6181 15.75 18.4432V17.7339C15.75 16.2974 15.1875 14.9896 14.2594 13.992C13.1578 12.8083 11.5453 12.059 9.75 12.059H6ZM20.25 20.5713H24C27.3141 20.5713 30 18.0309 30 14.8965V14.1871C30 13.0122 28.9922 12.059 27.75 12.059H16.5C15.9047 12.059 15.3656 12.2763 14.9625 12.6354C15.3234 12.9634 15.6516 13.3314 15.9375 13.7216C16.0734 13.5708 16.275 13.4777 16.5047 13.4777H27.7547C28.1672 13.4777 28.5047 13.7969 28.5047 14.1871V14.8965C28.5047 17.2462 26.4891 19.1526 24.0047 19.1526H20.2547C19.1016 19.1526 18.0516 18.7447 17.2547 18.0708V18.4432C17.2547 18.8777 17.1703 19.2945 17.0203 19.6758C17.9531 20.2432 19.0641 20.5713 20.2547 20.5713H20.25Z"
        fill={color}
      />
    </Svg>
  );
};

// Event Check Icon
export const EventCheckIcon = ({ color = "#3257DB", size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 34/30)} viewBox="0 0 30 34" fill="none">
      <G clipPath="url(#clip0_50_2740)">
        <Path
          d="M8.57143 0C9.46205 0 10.1786 0.710547 10.1786 1.59375V4.25H19.8214V1.59375C19.8214 0.710547 20.5379 0 21.4286 0C22.3192 0 23.0357 0.710547 23.0357 1.59375V4.25H25.7143C28.0781 4.25 30 6.15586 30 8.5V9.5625V12.75V29.75C30 32.0941 28.0781 34 25.7143 34H4.28571C1.92188 34 0 32.0941 0 29.75V12.75V9.5625V8.5C0 6.15586 1.92188 4.25 4.28571 4.25H6.96429V1.59375C6.96429 0.710547 7.6808 0 8.57143 0ZM26.7857 12.75H3.21429V29.75C3.21429 30.3344 3.69643 30.8125 4.28571 30.8125H25.7143C26.3036 30.8125 26.7857 30.3344 26.7857 29.75V12.75ZM22.0312 19.7227L14.5312 27.1602C13.9018 27.7844 12.8839 27.7844 12.2612 27.1602L7.97545 22.9102C7.34598 22.2859 7.34598 21.2766 7.97545 20.659C8.60491 20.0414 9.62277 20.0348 10.2455 20.659L13.3929 23.7801L19.7545 17.4715C20.3839 16.8473 21.4018 16.8473 22.0246 17.4715C22.6473 18.0957 22.654 19.1051 22.0246 19.7227H22.0312Z"
          fill={color}
        />
      </G>
      <Defs>
        <ClipPath id="clip0_50_2740">
          <Rect width="30" height="34" fill="white"/>
        </ClipPath>
      </Defs>
    </Svg>
  );
};

// User Icon
export const UserIcon = ({ color = "#014954", size = 21 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 23/21)} viewBox="0 0 21 23" fill="none">
      <Path
        d="M15 5.9654C15 5.40648 14.8836 4.85302 14.6575 4.33664C14.4313 3.82026 14.0998 3.35107 13.682 2.95585C13.2641 2.56063 12.768 2.24712 12.2221 2.03323C11.6761 1.81934 11.0909 1.70925 10.5 1.70925C9.90905 1.70925 9.32389 1.81934 8.77792 2.03323C8.23196 2.24712 7.73588 2.56063 7.31802 2.95585C6.90016 3.35107 6.56869 3.82026 6.34254 4.33664C6.1164 4.85302 6 5.40648 6 5.9654C6 6.52433 6.1164 7.07778 6.34254 7.59416C6.56869 8.11055 6.90016 8.57974 7.31802 8.97496C7.73588 9.37018 8.23196 9.68369 8.77792 9.89758C9.32389 10.1115 9.90905 10.2216 10.5 10.2216C11.0909 10.2216 11.6761 10.1115 12.2221 9.89758C12.768 9.68369 13.2641 9.37018 13.682 8.97496C14.0998 8.57974 14.4313 8.11055 14.6575 7.59416C14.8836 7.07778 15 6.52433 15 5.9654ZM4.5 5.9654C4.5 4.46033 5.13214 3.01691 6.25736 1.95266C7.38258 0.888415 8.9087 0.290527 10.5 0.290527C12.0913 0.290527 13.6174 0.888415 14.7426 1.95266C15.8679 3.01691 16.5 4.46033 16.5 5.9654C16.5 7.47048 15.8679 8.9139 14.7426 9.97815C13.6174 11.0424 12.0913 11.6403 10.5 11.6403C8.9087 11.6403 7.38258 11.0424 6.25736 9.97815C5.13214 8.9139 4.5 7.47048 4.5 5.9654ZM1.5 21.5713H19.5C19.4438 18.0378 16.3969 15.1871 12.6422 15.1871H8.35781C4.60781 15.1871 1.56094 18.0378 1.5 21.5713ZM0 21.6733C0 17.3063 3.74063 13.7684 8.35781 13.7684H12.6422C17.2594 13.7684 21 17.3063 21 21.6733C21 22.4004 20.3766 22.99 19.6078 22.99H1.39219C0.623438 22.99 0 22.4004 0 21.6733Z"
        fill={color}
      />
    </Svg>
  );
};

// Friends Icon
export const FriendsIcon = ({ color = "#00788D", size = 22 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 18/22)} viewBox="0 0 22 18" fill="none">
      <Path
        d="M13.739 10.6659C13.739 10.6659 8.09398 10.6659 5.97798 15.6049C8.4662 16.5121 11.0907 16.9893 13.739 17.0159C16.3873 16.9893 19.0118 16.5121 21.5 15.6049C19.383 10.6659 13.739 10.6659 13.739 10.6659ZM13.739 9.96591C15.856 9.96591 17.266 7.84891 17.266 4.32091C17.2761 3.85493 17.1917 3.39172 17.0181 2.95919C16.8444 2.52666 16.585 2.13375 16.2555 1.80412C15.9259 1.4745 15.5331 1.21498 15.1006 1.04119C14.6681 0.867409 14.205 0.782954 13.739 0.792914C13.273 0.783095 12.8099 0.867648 12.3775 1.04148C11.9451 1.21532 11.5523 1.47484 11.2228 1.80444C10.8933 2.13404 10.6339 2.52689 10.4602 2.95936C10.2864 3.39182 10.202 3.85497 10.212 4.32091C10.212 7.84391 11.622 9.96191 13.739 9.96191V9.96591ZM6.89098 7.55391C7.22627 7.55412 7.55767 7.48205 7.86259 7.34261C8.16752 7.20318 8.43881 6.99966 8.65798 6.74591C8.4697 5.94984 8.37837 5.13392 8.38598 4.31591C8.3537 3.8061 8.44221 3.29587 8.64429 2.82671C8.84638 2.35754 9.15634 1.9427 9.54898 1.61591C9.29277 1.13216 8.91062 0.726622 8.44293 0.442166C7.97523 0.15771 7.43936 0.00490859 6.89198 -8.61374e-05C6.50811 -0.00826787 6.12656 0.0613072 5.77029 0.204451C5.41402 0.347595 5.0904 0.561347 4.8189 0.832842C4.54741 1.10434 4.33366 1.42796 4.19051 1.78423C4.04737 2.1405 3.97779 2.52205 3.98598 2.90591C3.98598 5.81191 5.14698 7.55391 6.89098 7.55391ZM9.63298 9.83091L9.53298 8.68291C8.68861 8.35678 7.79639 8.17164 6.89198 8.13491C6.89198 8.13491 2.24398 8.13491 0.500977 12.2029C2.16003 12.8079 3.89416 13.1827 5.65498 13.3169C6.82948 11.9929 8.16599 10.822 9.63298 9.83191V9.83091Z"
        fill={color}
      />
    </Svg>
  );
};

// Avatar Icon
export const AvatarIcon = ({ color = "#FFFF00", size = 48 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 25/24)} viewBox="0 0 24 25" fill="none">
      <Path
        d="M11.859 15.093C11.859 15.093 3.234 15.093 0 22.639C3.80204 24.0254 7.8123 24.7544 11.859 24.795C15.9057 24.7544 19.916 24.0254 23.718 22.639C20.484 15.093 11.859 15.093 11.859 15.093ZM11.859 14.015C15.093 14.015 17.25 10.781 17.25 5.39002C17.25 -0.000976562 11.859 -0.000976562 11.859 -0.000976562C11.859 -0.000976562 6.467 2.38419e-05 6.467 5.39102C6.467 10.782 8.625 14.015 11.859 14.015Z"
        fill={color}
      />
    </Svg>
  );
};

// Camera Icon
export const CameraIcon = ({ color = "#015364", size = 16 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 14/16)} viewBox="0 0 16 14" fill="none">
      <Path
        d="M4.65938 1.025L4.33437 2H2C0.896875 2 0 2.89687 0 4V12C0 13.1031 0.896875 14 2 14H14C15.1031 14 16 13.1031 16 12V4C16 2.89687 15.1031 2 14 2H11.6656L11.3406 1.025C11.1375 0.4125 10.5656 0 9.91875 0H6.08125C5.43438 0 4.8625 0.4125 4.65938 1.025ZM8 5C8.79565 5 9.55871 5.31607 10.1213 5.87868C10.6839 6.44129 11 7.20435 11 8C11 8.79565 10.6839 9.55871 10.1213 10.1213C9.55871 10.6839 8.79565 11 8 11C7.20435 11 6.44129 10.6839 5.87868 10.1213C5.31607 9.55871 5 8.79565 5 8C5 7.20435 5.31607 6.44129 5.87868 5.87868C6.44129 5.31607 7.20435 5 8 5Z"
        fill={color}
      />
    </Svg>
  );
};

// Clock Icon
export const ClockIcon = ({ color = "#FFFF00", size = 13 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 14/13)} viewBox="0 0 13 14" fill="none">
      <Path
        d="M8.53252 9.94789L5.96151 7.37592V3.92932H7.52334V6.73004L9.63781 8.84451L8.53252 9.94789ZM6.74194 0.805664C5.50634 0.805664 4.29849 1.17206 3.27112 1.85853C2.24376 2.54499 1.44302 3.52069 0.97018 4.66223C0.497336 5.80378 0.373618 7.05991 0.614672 8.27177C0.855726 9.48363 1.45073 10.5968 2.32443 11.4705C3.19813 12.3442 4.31129 12.9392 5.52315 13.1803C6.73501 13.4213 7.99114 13.2976 9.13269 12.8247C10.2742 12.3519 11.2499 11.5512 11.9364 10.5238C12.6229 9.49643 12.9893 8.28858 12.9893 7.05298C12.9893 5.39609 12.3311 3.80706 11.1595 2.63546C9.98786 1.46386 8.39884 0.805664 6.74194 0.805664ZM6.74194 11.7385C5.81524 11.7385 4.90935 11.4637 4.13883 10.9488C3.3683 10.434 2.76775 9.7022 2.41312 8.84604C2.05849 7.98987 1.9657 7.04778 2.14649 6.13888C2.32728 5.22999 2.77353 4.39512 3.42881 3.73984C4.08408 3.08456 4.91896 2.63831 5.82785 2.45752C6.73675 2.27673 7.67884 2.36952 8.535 2.72415C9.39116 3.07879 10.1229 3.67934 10.6378 4.44986C11.1526 5.22039 11.4274 6.12628 11.4274 7.05298C11.4274 8.29565 10.9338 9.48742 10.0551 10.3661C9.17638 11.2448 7.98461 11.7385 6.74194 11.7385Z"
        fill={color}
      />
    </Svg>
  );
};

// Date Confirmed Icon
export const DateConfirmedIcon = ({ color = "#3257DB", size = 30 }: IconProps) => {
  return (
    <Svg width={size} height={Math.floor(size * 34/30)} viewBox="0 0 30 34" fill="none">
      <G clipPath="url(#clip0_46_682)">
        <Path
          d="M26.25 3.4H24.375V6.8H18.75V3.4H11.25V6.8H5.625V3.4H3.75C2.75544 3.40189 1.80161 3.79957 1.09835 4.50283C0.395088 5.20609 -0.00259399 6.15992 -0.00448227 7.15448V30.6C-0.00259399 31.5946 0.395088 32.5484 1.09835 33.2517C1.80161 33.9549 2.75544 34.3526 3.75 34.3545H26.25C27.2446 34.3526 28.1984 33.9549 28.9017 33.2517C29.6049 32.5484 30.0026 31.5946 30 30.6V7.15448C30.0026 6.15992 29.6049 5.20609 28.9017 4.50283C28.1984 3.79957 27.2446 3.40189 26.25 3.4ZM26.25 30.6H3.75V11.9H26.25V30.6ZM9.375 0H6.25V5.95H9.375V0ZM23.75 0H20.625V5.95H23.75V0Z"
          fill={color}
        />
        <Path
          d="M15 25.5C18.4518 25.5 21.25 22.7018 21.25 19.25C21.25 15.7982 18.4518 13 15 13C11.5482 13 8.75 15.7982 8.75 19.25C8.75 22.7018 11.5482 25.5 15 25.5Z"
          fill={color}
        />
        <Path
          d="M15 15.3L16.4 18.1L19.5 18.6L17.25 20.8L17.8 23.9L15 22.45L12.2 23.9L12.75 20.8L10.5 18.6L13.6 18.1L15 15.3Z"
          fill="white"
        />
      </G>
      <Defs>
        <ClipPath id="clip0_46_682">
          <Rect width="30" height="34" fill="white"/>
        </ClipPath>
      </Defs>
    </Svg>
  );
};
